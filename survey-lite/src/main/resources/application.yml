xmplus:
  domain: ${XMPLUS_DOMAIN:https://dev.xmplus.cn}
  short: ${XMPLUS_SHORT:https://dev-t.xmplus.cn}
  response-shared-expire-time: ${RESPONSE_SHARED_EXPIRE_TIME:30}
server:
  forward-headers-strategy: native
  shutdown: graceful
  port: ${PORT:8082}
  servlet:
    context-path: /api/survey
  error:
    whitelabel:
      enabled: false
      path: ${SERVER_ERROR_PATH:https://dev.xmplus.cn/lite/error}
  tomcat:
    accept-count: 100 # 并发线程数
    threads:
      max: ${TOMCAT_MAX_THREADS:400}


nacos:
  config:
    server-addr: 123mse-8ba4aca6-p.nacos-ans.mse.aliyuncs.com
    dataId: cipher-kms-aes-256-test
    group: test
    namespace: public
    bootstrap:
      enable: true
      log-enable: true

spring:
  application:
    name: cipher-kms-aes-256-test
  lifecycle:
    timeout-per-shutdown-phase: 60s
  config:
    import:
      - classpath:befun-auth.yml
      - classpath:befun-xss.yml
      - classpath:befun-metrics.yml
      - classpath:flyway.yml
      - classpath:mail.yml
      - classpath:befun-sms-chuanglan.yml
      - classpath:befun-xpack-wechatopen.yml
      - classpath:befun-xpack-wechatpay.yml
      - classpath:befun-xpack-inboxmessage.yml
      - classpath:file.yml
      - classpath:befun-template-file.yml
  servlet:
    multipart:
      max-file-size: 50MB
      max-request-size: 50MB
  jackson:
    date-format: yyyy-MM-dd HH:mm:ss
    time-zone: Asia/Shanghai
  mvc:
    throw-exception-if-no-handler-found: true
  datasource:
    driver-class-name: com.mysql.cj.jdbc.Driver
    jdbc-url: ${MYSQL_URL:*********************************************************************************************************************************}
    username: ${MYSQL_USER:surveyuser}
    password: ${MYSQL_PASSWORD:C1E9M-P2l0t}
    connection-init-sql: SET NAMES utf8mb4
    connection-test-query: SELECT 1 FROM DUAL
    maximum-pool-size: ${MYSQL_MAX_POOL_SIZE:10} # 最大连接数, 核心数2倍+核心数
  jpa:
    generate-ddl: false
    show-sql: ${SHOW_SQL:true}
    hibernate:
      ddl-auto: none
    properties:
      hibernate:
        dialect: cn.hanyi.survey.dialect.ExtendMySqlDialect
        enable_lazy_load_no_trans: true
        # 批处理
        jdbc.batch_size: 50
        order_inserts: true
        #        generate_statistics: true
        SQL_SLOW: ${LOG_LEVEL:info}
        session:
          events:
            log:
              LOG_QUERIES_SLOWER_THAN_MS: 1000
  #        connection.handling_mode: DELAYED_ACQUISITION_AND_RELEASE_AFTER_TRANSACTION
  data:
    redis.repositories.enabled: false
    jdbc.repositories.enabled: false
    rest:
      default-media-type: application/json
  cache:
    type: redis
    redis.time-to-live: 60m
  redis:
    host: ${REDIS_HOST:*********}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:bsxrzQ2l2zm}

logging:
  level:
    root: ${LOG_LEVEL:info}
    org.befun: ${LOG_LEVEL_BEFUN:info}
    cn.hanyi.survey.core.service.quota: ${LOG_LEVEL:debug}
#    org.hibernate.type.descriptor.sql.BasicBinder: trace
    org.hibernate:
      SQL: ${LOG_LEVEL_SQL:error}
      type.descriptor.sql: ${LOG_LEVEL_SQL:error}


hanyi:
  common:
    ip-resolver:
      default-platform: local
      local:
        algorithm: memory
    contacts:
      standardContact: ${STANDARD_CONTACT:瑞华}
      users:
        - id: 1
          contacts: ${SALES_USER_NAME_1:巫亮}
          mobile: ${SALES_USER_PHONE_1:18824591379}
          qrcode: ${SALES_USER_QRCODE_1:https://hy-assets.oss-cn-shenzhen.aliyuncs.com/qrcode/qrcode-wuliang.jpg}
        - id: 2
          contacts: ${SALES_USER_NAME_2:庆丰}
          mobile: ${SALES_USER_PHONE_2:18823797224}
          qrcode: ${SALES_USER_QRCODE_2:https://hy-assets.oss-cn-shenzhen.aliyuncs.com/qrcode/qrcode-wumeng.png}
#        - id: 3
#          contacts: ${SALES_USER_NAME_3:李灵琪}
#          mobile: ${SALES_USER_PHONE_3:18824597621}
#          qrcode: ${SALES_USER_QRCODE_3:https://hy-assets.oss-cn-shenzhen.aliyuncs.com/qrcode/qrcode-lilingqi.png}

consumer:
  group-default: backend-lite
  topic-register-user: queuing-user-create
  topic-rollback-response: survey-response-rollback

springdoc:
  swagger-ui.enabled: ${ENABLE_DOC:false}
  api-docs.enabled: ${ENABLE_DOC:false}

survey:
  client.enabled: ${SURVEY_CLIENT_ENABLED:true}
  cem-domain: ${CEM_DOMAIN:https://dev.xmplus.cn/cem/}
  csv-file-max: ${CSV_FILE_MAX:1} # csv文件大小
  send-manage-file-max: ${SEND_MANAGE_FILE_MAX:5}
  #  send-ignore: false # 本地测试的时候，设置为true不真实发送短信和微信
  enable-limit-sms: ${ENABLE_LIMIT_SMS:true}
  free-analysis-limit: ${FREE_ANALYSIS_LIMIT:5000}
  cache:
    enabled: ${SURVEY_CACHE_ENABLED:true}
  survey-url-prefix:
    root: ${SURVEY_URL_PREFIX:https://dev.xmplus.cn/lite/}
    path: ${SURVEY_URL_PATH:lite}
  quota:
    sync:
      size-per-page: 500
  expression:
    context-max-num: ${CONTEXT_MAX_NUM:100}
    expression-max-num: ${EXPRESSION_MAX_NUM:100}
  download:
    batch: ${SURVEY_DOWNLOAD_BATCH:500}
    attachment:
      freeMsg: ${SURVEY_ATTACHMENT_FREE_MSG:{ATTACHMENT_VERSION}本导出上限：{ATTACHMENT_LIMIT}G，当前附件详情为{CURRENT_ATTACHMENT_SIZE}G，已经达到版本限额啦，抓紧去升级吧！}
      paidMsg: ${SURVEY_ATTACHMENT_PAID_MSG:{ATTACHMENT_VERSION}版本导出上限：{ATTACHMENT_LIMIT}G，当前附件详情为{CURRENT_ATTACHMENT_SIZE}G，可联系我们处理！}
      empty: 0
      free: ${SURVEY_ATTACHMENT_FREE_SIZE:1}
      base: ${SURVEY_ATTACHMENT_BASE_SIZE:10}
      update: ${SURVEY_ATTACHMENT_UPDATE_SIZE:10}
      profession: ${SURVEY_ATTACHMENT_PROFESSION_SIZE:10}
      systemCapacity: ${SURVEY_ATTACHMENT_SYSTEM_CAPACITY:15}  # 附件同时下载支持的容量
  response:
    import-file-max: ${RESPONSE_IMPORT_FILE_MAX:5} #答卷导入限制5M以下
    import-size: ${RESPONSE_IMPORT_SIZE:10000}  #答卷导入的数量限制低于1w
  lottery:
    red-pack:
      # 未领取退还红包时间 单位：分钟 25*60 分
      refundRedPack: ${SURVEY_LOTTERY_REDPACK:1500}
      refundRedPackDelay: ${SURVEY_LOTTERY_REDPACK_DELAY:2880}
  enable-content-audit: ${SURVEY_ENABLE_CONTENT_AUDIT:true}
  public-template-org-id: ${PUBLIC_TEMPLATE_ORG_ID:1122653153820672}
befun:
  server:
    enable-open-api-filter: true
  task:
    type: redisStream
    redis-stream:
      prefix: ${BEFUN_TASK_PREFIX:befun.task}
    queue:
      adminx-sync-quota-key: ${ADMINX_SYNC_QUOTA_KEY:befun.task.queue.adminx-sync-quota}
  extension:
    http-log.enable: ${LOG_HTTP:false}
    update-file.enable: true
    mail.enable: true
    wechat-open:
      enable: true
      component-app-id: ${WECHAT_OPEN_APP_ID:wxbb2e0ad30fee2502}
      component-secret: ${WECHAT_OPEN_APP_SECRET:372012a24728ce35610b37e8eb539538}
      component-token: ${WECHAT_OPEN_TOKEN:surveyplus}
      component-aes-key: ${WECHAT_OPEN_AES_KEY:0F68wpOxqNJs4bL5xUoCyrlWinYF6QNPtmYVx8Ex0BB}
    wechat-mp:
      enable: true
      app-id: ${WECHAT_MP_CEM_APP_ID:wx38eb115cf5014c74}
      app-secret: ${WECHAT_MP_CEM_APP_SECRET:03eca9a58e86f8e376e6cc2ea33a06c0}
      token: ${WECHAT_MP_CEM_TOKEN:surveyplus20190710surveyplus}
    smart-verify:
      enable: ${SMART_VERIFY_ENABLE:true}
      regionId: ${SMART_VERIFY_REGION:cn-hangzhou}
      accessKeyId: ${SMART_VERIFY_ACCESS_KEY:LTAI4G5RfyxPtajMojKJPvmM}
      accessKeySecret: ${SMART_VERIFY_ACCESS_SECRET:******************************}
      product: afs
      domain: afs.aliyuncs.com
      appKey: ${SMART_VERIFY_APP_KEY:FFFF0N00000000008B36}
    shorturl:
      root: ${SHORTURL:https://dev-t.xmplus.cn}
      survey-client-prefix: ${SURVEY_CLIENT_PREFIX:https://dev.xmplus.cn/lite/l}
    wechat-miniprogram:
      enable: true
      app-id: ${WECHAT_MINIPROGRAM_APP_ID:wx11defe095afaf247}
      app-secret: ${WECHAT_MINIPROGRAM_SECRET:982e41fee09416518ebf67bc91f24740}
      token: ${WECHAT_MINIPROGRAM_TOKEN:surveyplus}
      aes-key: ${WECHAT_MINIPROGRAM_AES_KEY:0F68wpOxqNJs4bL5xUoCyrlWinYF6QNPtmYVx8Ex0BB}
      version: ${WECHAT_MINIPROGRAM_VERSION:trial}
    wechat-pay:
      enable: ${WECHAT_PAY_ENABLE:true}
      app-id: ${WECHAT_PAY_APP_ID:wxbe9320f8f7c9b5ca}
      mch-id: ${WECHAT_PAY_MCH_ID:1521879911}
      v2-mch-key: ${WECHAT_PAY_V2_MCH_KEY:LDPpsBzLOqo6dVjfGj2wWXYZHa9UdcO7}
      v3-mch-key: ${WECHAT_PAY_V3_MCH_KEY:c0R7uY7aiRljM6eXLFYB2HBqZ3kw0KTN}
      cert-p12-path: ${WECHAT_PAY_CERT_P12_PATH:/config/cert/wx/apiclient_cert.p12}
      private-key-path: ${WECHAT_PAY_PRIVATE_KEY_PATH:/config/cert/wx/apiclient_key.pem}
      cert-pem-path: ${WECHAT_PAY_CERT_PEM_PATH:/config/cert/wx/apiclient_cert.pem}
      use-sandbox: ${WECHAT_PAY_USE_SANDBOX:false}
      pay-notify-url: ${WECHAT_PAY_NOTIFY_URL:${xmplus.domain}/api/auth/wechatPay/placeOrder/callback/cem}

