package cn.hanyi.survey;

import org.befun.auth.Aspects.TenantContext;
import org.befun.core.repository.impl.BaseRepositoryImpl;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.domain.EntityScan;
import org.springframework.cache.annotation.EnableCaching;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.data.jpa.repository.config.EnableJpaRepositories;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.scheduling.annotation.EnableScheduling;

import java.util.concurrent.ExecutorService;
import java.util.concurrent.LinkedBlockingDeque;
import java.util.concurrent.ThreadPoolExecutor;
import java.util.concurrent.TimeUnit;

@SpringBootApplication(scanBasePackages = {"cn.hanyi", "org.befun.core"})
@EnableAsync(proxyTargetClass = true)
@EnableCaching(proxyTargetClass = true)
@EnableScheduling
public class SurveyApplication {

    @Configuration
    @EntityScan({
            "cn.hanyi.survey.core.entity",
            "org.befun.core.entity",
            "cn.hanyi.ctm.entity",
            "org.befun.task.entity",
            "org.befun.auth.entity",
            "org.befun.extension.entity",
            "cn.hanyi.cem.core.entity",
    })
    @EnableJpaRepositories(
            basePackages = {
                    "cn.hanyi.survey.core.repository",
                    "org.befun.core.repo",
                    "cn.hanyi.ctm.repository",
                    "org.befun.task.repository",
                    "org.befun.auth.repository",
                    "org.befun.extension.repository",
                    "cn.hanyi.cem.core.repository",
            },
            repositoryBaseClass = BaseRepositoryImpl.class)
    public class SurveyJPAConfig {
    }

    @Bean("downloadThreadPool")
    public ExecutorService getThreadPool() {
        return new ThreadPoolExecutor(2, 5, 10, TimeUnit.SECONDS,
                new LinkedBlockingDeque<>());
    }

    @Bean
    @ConditionalOnMissingBean
    public TenantContext openApiTenantContext(){
        return new TenantContext();
    }

    public static void main(String[] args) {
        SpringApplication.run(SurveyApplication.class, args);
    }
}